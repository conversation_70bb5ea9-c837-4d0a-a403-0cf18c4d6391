{% extends "base.html" %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="bi bi-people-fill me-2"></i>Hiring Managers
                </h1>
                <button class="btn btn-primary" onclick="refreshData()">
                    <i class="bi bi-arrow-clockwise me-2"></i>Refresh Data
                </button>
            </div>
        </div>
    </div>

    <!-- Search Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-funnel me-2"></i>Search Filters
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label for="companyFilter" class="form-label">Company</label>
                            <input type="text" class="form-control" id="companyFilter" 
                                   placeholder="e.g., Google, Microsoft">
                        </div>
                        <div class="col-md-4">
                            <label for="locationFilter" class="form-label">Location</label>
                            <input type="text" class="form-control" id="locationFilter" 
                                   placeholder="e.g., India, Bangalore">
                        </div>
                        <div class="col-md-4">
                            <label for="industryFilter" class="form-label">Industry</label>
                            <select class="form-select" id="industryFilter">
                                <option value="">All Industries</option>
                                <option value="Technology">Technology</option>
                                <option value="IT Services">IT Services</option>
                                <option value="Finance">Finance</option>
                                <option value="Healthcare">Healthcare</option>
                                <option value="Manufacturing">Manufacturing</option>
                                <option value="Consulting">Consulting</option>
                                <option value="E-commerce">E-commerce</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <button class="btn btn-primary me-2" onclick="searchHiringManagers()">
                                <i class="bi bi-search me-2"></i>Search
                            </button>
                            <button class="btn btn-outline-secondary" onclick="clearFilters()">
                                <i class="bi bi-x-circle me-2"></i>Clear Filters
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Indicator -->
    <div id="loadingIndicator" class="text-center py-4" style="display: none;">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-2">Loading hiring managers...</p>
    </div>

    <!-- Results Summary -->
    <div id="resultsSummary" class="row mb-3" style="display: none;">
        <div class="col-12">
            <div class="alert alert-info">
                <i class="bi bi-info-circle me-2"></i>
                Found <span id="resultsCount">0</span> hiring managers
                <span id="searchCriteria"></span>
            </div>
        </div>
    </div>

    <!-- Hiring Managers Grid -->
    <div id="hiringManagersGrid" class="row g-4">
        <!-- Hiring manager cards will be populated here -->
    </div>

    <!-- No Results Message -->
    <div id="noResults" class="text-center py-5" style="display: none;">
        <i class="bi bi-search text-muted" style="font-size: 3rem;"></i>
        <h4 class="text-muted mt-3">No hiring managers found</h4>
        <p class="text-muted">Try adjusting your search criteria or refresh the data.</p>
    </div>

    <!-- Error Message -->
    <div id="errorMessage" class="alert alert-danger" style="display: none;">
        <i class="bi bi-exclamation-triangle me-2"></i>
        <span id="errorText"></span>
    </div>
</div>

<!-- Email Modal -->
<div class="modal fade" id="emailModal" tabindex="-1" aria-labelledby="emailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="emailModalLabel">
                    <i class="bi bi-envelope me-2"></i>Send Email to Hiring Manager
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="emailForm">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="emailTo" class="form-label">To</label>
                            <input type="email" class="form-control" id="emailTo" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="emailHiringManager" class="form-label">Hiring Manager</label>
                            <input type="text" class="form-control" id="emailHiringManager" readonly>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="emailCompany" class="form-label">Company</label>
                            <input type="text" class="form-control" id="emailCompany" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="emailJobTitle" class="form-label">Job Title</label>
                            <input type="text" class="form-control" id="emailJobTitle" readonly>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="emailSubject" class="form-label">Subject</label>
                        <input type="text" class="form-control" id="emailSubject" 
                               placeholder="Application for [Job Title] at [Company]">
                    </div>
                    <div class="mb-3">
                        <label for="emailMessage" class="form-label">Message</label>
                        <textarea class="form-control" id="emailMessage" rows="8" 
                                  placeholder="Dear [Hiring Manager Name],

I am writing to express my interest in the [Job Title] position at [Company]. 

[Your message here]

Best regards,
[Your Name]"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="sendEmail()">
                    <i class="bi bi-send me-2"></i>Send Email
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Success Toast -->
<div class="toast-container position-fixed bottom-0 end-0 p-3">
    <div id="successToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header bg-success text-white">
            <i class="bi bi-check-circle me-2"></i>
            <strong class="me-auto">Success</strong>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="successMessage">
            Email sent successfully!
        </div>
    </div>
</div>

<!-- Error Toast -->
<div class="toast-container position-fixed bottom-0 end-0 p-3">
    <div id="errorToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header bg-danger text-white">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <strong class="me-auto">Error</strong>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="errorToastMessage">
            An error occurred.
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/hiring_managers.js') }}"></script>
{% endblock %}
