import requests
import time
from typing import List, Dict, Optional
from config import Config
import json

class HiringManagerAPI:
    """
    RapidAPI Hiring Manager API integration for recruitment data
    Provides access to hiring manager information and recruitment insights
    """
    
    def __init__(self):
        self.rapidapi_key = Config.RAPIDAPI_KEY
        self.base_url = "https://hiring-manager-api.p.rapidapi.com"
        
        # Common headers for RapidAPI
        self.headers = {
            'x-rapidapi-host': 'hiring-manager-api.p.rapidapi.com',
            'x-rapidapi-key': self.rapidapi_key,
            'Content-Type': 'application/json'
        }
        
        # Available endpoints (based on common hiring manager API patterns)
        self.endpoints = {
            'recruitment_manager_24h': '/recruitment-manager-24h',
            'hiring_managers': '/hiring-managers',
            'recruitment_data': '/recruitment-data',
            'job_postings': '/job-postings',
            'company_hiring': '/company-hiring'
        }
    
    def get_recruitment_managers_24h(self) -> Dict:
        """
        Get recruitment managers data from the last 24 hours
        """
        print("🔍 Fetching recruitment managers data (24h)...")
        
        if not self.rapidapi_key:
            return {'error': 'RapidAPI key not configured'}
        
        try:
            url = f"{self.base_url}{self.endpoints['recruitment_manager_24h']}"
            response = requests.get(url, headers=self.headers, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                return {
                    'success': True,
                    'data': data,
                    'source': 'Hiring Manager API',
                    'timestamp': time.time()
                }
            elif response.status_code == 403:
                return {
                    'error': 'API subscription required',
                    'message': 'You need to subscribe to this API on RapidAPI',
                    'status_code': 403
                }
            else:
                return {
                    'error': f'API request failed with status {response.status_code}',
                    'message': response.text,
                    'status_code': response.status_code
                }
                
        except requests.exceptions.RequestException as e:
            return {'error': f'Request failed: {str(e)}'}
        except Exception as e:
            return {'error': f'Unexpected error: {str(e)}'}
    
    def search_hiring_managers(self, company_name: str = None, location: str = None, 
                             industry: str = None) -> Dict:
        """
        Search for hiring managers based on criteria
        """
        print(f"🔍 Searching hiring managers for: {company_name or 'All companies'}")
        
        if not self.rapidapi_key:
            return {'error': 'RapidAPI key not configured'}
        
        try:
            url = f"{self.base_url}{self.endpoints['hiring_managers']}"
            
            # Build query parameters
            params = {}
            if company_name:
                params['company'] = company_name
            if location:
                params['location'] = location
            if industry:
                params['industry'] = industry
            
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                return {
                    'success': True,
                    'data': data,
                    'search_criteria': {
                        'company': company_name,
                        'location': location,
                        'industry': industry
                    },
                    'source': 'Hiring Manager API',
                    'timestamp': time.time()
                }
            elif response.status_code == 403:
                return {
                    'error': 'API subscription required',
                    'message': 'You need to subscribe to this API on RapidAPI',
                    'status_code': 403
                }
            else:
                return {
                    'error': f'API request failed with status {response.status_code}',
                    'message': response.text,
                    'status_code': response.status_code
                }
                
        except requests.exceptions.RequestException as e:
            return {'error': f'Request failed: {str(e)}'}
        except Exception as e:
            return {'error': f'Unexpected error: {str(e)}'}
    
    def get_company_hiring_data(self, company_name: str) -> Dict:
        """
        Get hiring data for a specific company
        """
        print(f"🔍 Fetching hiring data for: {company_name}")
        
        if not self.rapidapi_key:
            return {'error': 'RapidAPI key not configured'}
        
        try:
            url = f"{self.base_url}{self.endpoints['company_hiring']}"
            params = {'company': company_name}
            
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                return {
                    'success': True,
                    'company': company_name,
                    'hiring_data': data,
                    'source': 'Hiring Manager API',
                    'timestamp': time.time()
                }
            elif response.status_code == 403:
                return {
                    'error': 'API subscription required',
                    'message': 'You need to subscribe to this API on RapidAPI',
                    'status_code': 403,
                    'subscription_url': 'https://rapidapi.com/hiring-manager-api'
                }
            else:
                return {
                    'error': f'API request failed with status {response.status_code}',
                    'message': response.text,
                    'status_code': response.status_code
                }
                
        except requests.exceptions.RequestException as e:
            return {'error': f'Request failed: {str(e)}'}
        except Exception as e:
            return {'error': f'Unexpected error: {str(e)}'}
    
    def test_api_connection(self) -> Dict:
        """
        Test the API connection and subscription status
        """
        print("🔧 Testing Hiring Manager API connection...")
        
        if not self.rapidapi_key:
            return {
                'success': False,
                'error': 'RapidAPI key not configured',
                'message': 'Please add RAPIDAPI_KEY to your .env file'
            }
        
        # Try the simplest endpoint first
        result = self.get_recruitment_managers_24h()
        
        if 'error' in result:
            if result.get('status_code') == 403:
                return {
                    'success': False,
                    'subscription_required': True,
                    'message': 'API key is valid but subscription is required',
                    'next_steps': [
                        'Visit https://rapidapi.com/hiring-manager-api',
                        'Subscribe to the Hiring Manager API',
                        'Choose an appropriate pricing plan',
                        'Test the connection again'
                    ]
                }
            else:
                return {
                    'success': False,
                    'error': result['error'],
                    'message': 'API connection failed'
                }
        else:
            return {
                'success': True,
                'message': 'API connection successful',
                'subscription_active': True
            }
    
    def get_alternative_apis(self) -> Dict:
        """
        Suggest alternative APIs for hiring manager data
        """
        return {
            'alternatives': [
                {
                    'name': 'LinkedIn Sales Navigator API',
                    'description': 'Access to LinkedIn professional profiles and hiring managers',
                    'url': 'https://developer.linkedin.com/',
                    'features': ['Professional profiles', 'Company data', 'Job postings']
                },
                {
                    'name': 'Apollo.io API',
                    'description': 'B2B contact database with hiring manager information',
                    'url': 'https://apolloapi.com/',
                    'features': ['Contact search', 'Email finder', 'Company data'],
                    'status': 'Already integrated in your app'
                },
                {
                    'name': 'Hunter.io API',
                    'description': 'Email finder and verification service',
                    'url': 'https://hunter.io/api',
                    'features': ['Email finder', 'Email verification', 'Domain search']
                },
                {
                    'name': 'Clearbit API',
                    'description': 'Company and person enrichment data',
                    'url': 'https://clearbit.com/docs',
                    'features': ['Person enrichment', 'Company data', 'Email finder']
                },
                {
                    'name': 'ZoomInfo API',
                    'description': 'B2B contact and company database',
                    'url': 'https://www.zoominfo.com/business/api',
                    'features': ['Contact database', 'Company insights', 'Intent data']
                }
            ],
            'recommendation': 'Consider using Apollo.io (already integrated) combined with LinkedIn Sales Navigator for comprehensive hiring manager data'
        }
