import requests
import time
from typing import List, Dict, Optional
from config import Config

class ApolloEnhanced:
    """
    Enhanced Apollo.io integration for finding HR professionals
    Uses Apollo's People Search API with advanced filtering
    """
    
    def __init__(self):
        self.api_key = Config.APOLLO_API_KEY
        self.base_url = "https://api.apollo.io/api/v1"
        self.people_search_url = f"{self.base_url}/people/search"
        self.organizations_search_url = f"{self.base_url}/organizations/search"
        self.person_enrich_url = f"{self.base_url}/people/match"
        
        # Common headers
        self.headers = {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache',
            'X-Api-Key': self.api_key
        }
    
    def find_hr_professionals_advanced(self, company_name: str, company_domain: str = None) -> List[Dict]:
        """
        Advanced HR professional search using Apollo's People Search API
        """
        if not self.api_key:
            print("❌ Apollo API key not configured")
            return []
        
        print(f"\n🚀 Apollo Enhanced: Advanced HR search for {company_name}")
        
        all_results = []
        
        # Step 1: Find the organization first to get accurate company data
        organization_data = self._find_organization(company_name, company_domain)
        if organization_data:
            print(f"  ✅ Found organization: {organization_data.get('name')} ({organization_data.get('primary_domain')})")
            company_domain = organization_data.get('primary_domain') or company_domain
        
        # Step 2: Search for HR professionals using multiple strategies
        search_strategies = [
            self._search_by_job_titles,
            self._search_by_departments,
            self._search_by_keywords,
            self._search_by_seniority_levels
        ]
        
        for strategy in search_strategies:
            try:
                results = strategy(company_name, company_domain, organization_data)
                if results:
                    all_results.extend(results)
                    print(f"  ✅ {strategy.__name__}: Found {len(results)} contacts")
                else:
                    print(f"  ❌ {strategy.__name__}: No results")
                
                # Rate limiting
                time.sleep(1)
                
            except Exception as e:
                print(f"  ⚠️ Error in {strategy.__name__}: {e}")
                continue
        
        # Step 3: Enrich contacts with additional data
        enriched_results = self._enrich_contacts(all_results)
        
        # Step 4: Deduplicate and rank
        final_results = self._deduplicate_and_rank_apollo(enriched_results)
        
        print(f"  📊 Apollo Summary: {len(all_results)} raw → {len(final_results)} final results")
        
        return final_results[:20]  # Return top 20 results
    
    def _find_organization(self, company_name: str, company_domain: str = None) -> Optional[Dict]:
        """Find organization data using Apollo's organization search"""
        try:
            search_params = {
                "organization_names": [company_name],
                "per_page": 5
            }
            
            if company_domain:
                search_params["organization_domains"] = [company_domain]
            
            response = requests.post(
                self.organizations_search_url,
                headers=self.headers,
                json=search_params,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                organizations = data.get('organizations', [])
                
                # Find best match
                for org in organizations:
                    org_name = org.get('name', '').lower()
                    if (company_name.lower() in org_name or 
                        org_name in company_name.lower() or
                        (company_domain and company_domain in org.get('primary_domain', ''))):
                        return org
                
                # Return first result if no exact match
                return organizations[0] if organizations else None
            
        except Exception as e:
            print(f"    Organization search error: {e}")
        
        return None
    
    def _search_by_job_titles(self, company_name: str, company_domain: str, org_data: Dict = None) -> List[Dict]:
        """Search by specific HR job titles"""
        results = []
        
        # Comprehensive list of HR job titles
        hr_titles = [
            "HR Manager", "Human Resources Manager", "HR Director", "HR Business Partner",
            "Talent Acquisition Manager", "Talent Acquisition Specialist", "Recruiter",
            "Senior Recruiter", "Talent Manager", "People Operations Manager",
            "HR Generalist", "HR Specialist", "Chief People Officer", "VP Human Resources",
            "Head of HR", "Head of People", "Director of Talent", "Talent Acquisition Lead",
            "People Partner", "HR Coordinator", "Recruitment Manager", "Talent Sourcer"
        ]
        
        for title in hr_titles[:10]:  # Limit to avoid quota exhaustion
            try:
                search_params = {
                    "person_titles": [title],
                    "organization_names": [company_name],
                    "per_page": 25
                }
                
                if company_domain:
                    search_params["organization_domains"] = [company_domain]
                
                # Add location filter for India
                search_params["person_locations"] = ["India"]
                
                response = requests.post(
                    self.people_search_url,
                    headers=self.headers,
                    json=search_params,
                    timeout=30
                )
                
                if response.status_code == 200:
                    data = response.json()
                    people = data.get('people', [])
                    
                    for person in people:
                        contact = self._format_apollo_contact(person, 'Job Title Search')
                        if contact:
                            results.append(contact)
                
                time.sleep(0.5)  # Rate limiting
                
            except Exception as e:
                print(f"      Title search error for {title}: {e}")
                continue
        
        return results
    
    def _search_by_departments(self, company_name: str, company_domain: str, org_data: Dict = None) -> List[Dict]:
        """Search by HR departments"""
        results = []
        
        hr_departments = [
            "Human Resources", "People Operations", "Talent Acquisition", 
            "Recruitment", "People & Culture", "HR"
        ]
        
        for department in hr_departments:
            try:
                search_params = {
                    "organization_names": [company_name],
                    "person_departments": [department],
                    "per_page": 25
                }
                
                if company_domain:
                    search_params["organization_domains"] = [company_domain]
                
                search_params["person_locations"] = ["India"]
                
                response = requests.post(
                    self.people_search_url,
                    headers=self.headers,
                    json=search_params,
                    timeout=30
                )
                
                if response.status_code == 200:
                    data = response.json()
                    people = data.get('people', [])
                    
                    for person in people:
                        contact = self._format_apollo_contact(person, 'Department Search')
                        if contact:
                            results.append(contact)
                
                time.sleep(0.5)
                
            except Exception as e:
                print(f"      Department search error for {department}: {e}")
                continue
        
        return results
    
    def _search_by_keywords(self, company_name: str, company_domain: str, org_data: Dict = None) -> List[Dict]:
        """Search by HR-related keywords"""
        results = []
        
        hr_keywords = [
            "human resources", "talent acquisition", "recruitment", "hiring",
            "people operations", "employee relations", "HR business partner"
        ]
        
        for keyword in hr_keywords[:5]:  # Limit searches
            try:
                search_params = {
                    "organization_names": [company_name],
                    "keywords": keyword,
                    "per_page": 20
                }
                
                if company_domain:
                    search_params["organization_domains"] = [company_domain]
                
                search_params["person_locations"] = ["India"]
                
                response = requests.post(
                    self.people_search_url,
                    headers=self.headers,
                    json=search_params,
                    timeout=30
                )
                
                if response.status_code == 200:
                    data = response.json()
                    people = data.get('people', [])
                    
                    for person in people:
                        contact = self._format_apollo_contact(person, 'Keyword Search')
                        if contact:
                            results.append(contact)
                
                time.sleep(0.5)
                
            except Exception as e:
                print(f"      Keyword search error for {keyword}: {e}")
                continue
        
        return results
    
    def _search_by_seniority_levels(self, company_name: str, company_domain: str, org_data: Dict = None) -> List[Dict]:
        """Search by seniority levels in HR"""
        results = []
        
        seniority_levels = ["Manager", "Director", "VP", "Senior", "Lead", "Head"]
        
        for level in seniority_levels[:4]:  # Limit searches
            try:
                search_params = {
                    "organization_names": [company_name],
                    "person_seniorities": [level],
                    "keywords": "human resources OR talent OR recruitment",
                    "per_page": 15
                }
                
                if company_domain:
                    search_params["organization_domains"] = [company_domain]
                
                search_params["person_locations"] = ["India"]
                
                response = requests.post(
                    self.people_search_url,
                    headers=self.headers,
                    json=search_params,
                    timeout=30
                )
                
                if response.status_code == 200:
                    data = response.json()
                    people = data.get('people', [])
                    
                    for person in people:
                        contact = self._format_apollo_contact(person, 'Seniority Search')
                        if contact:
                            results.append(contact)
                
                time.sleep(0.5)
                
            except Exception as e:
                print(f"      Seniority search error for {level}: {e}")
                continue
        
        return results
    
    def _format_apollo_contact(self, person: Dict, search_type: str) -> Optional[Dict]:
        """Format Apollo person data into standardized contact format"""
        try:
            first_name = person.get('first_name', '')
            last_name = person.get('last_name', '')
            name = f"{first_name} {last_name}".strip()
            
            email = person.get('email')
            title = person.get('title', '')
            linkedin_url = person.get('linkedin_url', '')
            
            # Get organization info
            organization = person.get('organization', {})
            company = organization.get('name', '')
            
            # Skip if no email or name
            if not email or not name:
                return None
            
            # Calculate confidence based on data completeness
            confidence = 90  # Base confidence for Apollo data
            if linkedin_url:
                confidence += 5
            if title and any(hr_word in title.lower() for hr_word in ['hr', 'human', 'talent', 'recruit']):
                confidence += 5
            
            return {
                'name': name,
                'email': email,
                'position': title,
                'company': company,
                'linkedin_url': linkedin_url,
                'source': f'Apollo {search_type}',
                'confidence': min(100, confidence),
                'location': 'India',
                'verified': True,
                'contact_method': 'Email + LinkedIn',
                'api_source': 'apollo_enhanced',
                'apollo_id': person.get('id'),
                'department': person.get('departments', []),
                'seniority': person.get('seniority')
            }
            
        except Exception as e:
            print(f"      Error formatting Apollo contact: {e}")
            return None

    def _enrich_contacts(self, contacts: List[Dict]) -> List[Dict]:
        """Enrich contacts with additional Apollo data"""
        enriched_contacts = []

        for contact in contacts:
            try:
                # Skip if already enriched or no Apollo ID
                if not contact.get('apollo_id'):
                    enriched_contacts.append(contact)
                    continue

                # Try to get additional person data
                enriched_data = self._get_person_details(contact['apollo_id'])
                if enriched_data:
                    contact.update(enriched_data)

                enriched_contacts.append(contact)

                # Rate limiting
                time.sleep(0.3)

            except Exception as e:
                print(f"      Enrichment error: {e}")
                enriched_contacts.append(contact)
                continue

        return enriched_contacts

    def _get_person_details(self, apollo_id: str) -> Optional[Dict]:
        """Get detailed person information from Apollo"""
        try:
            url = f"{self.base_url}/people/{apollo_id}"
            response = requests.get(url, headers=self.headers, timeout=15)

            if response.status_code == 200:
                data = response.json()
                person = data.get('person', {})

                # Extract additional useful information
                additional_data = {}

                if person.get('phone_numbers'):
                    additional_data['phone'] = person['phone_numbers'][0].get('raw_number')

                if person.get('employment_history'):
                    additional_data['employment_history'] = person['employment_history'][:3]  # Last 3 jobs

                if person.get('education'):
                    additional_data['education'] = person['education'][:2]  # Top 2 education entries

                return additional_data

        except Exception as e:
            print(f"      Person details error: {e}")

        return None

    def _deduplicate_and_rank_apollo(self, contacts: List[Dict]) -> List[Dict]:
        """Deduplicate and rank Apollo contacts"""
        if not contacts:
            return []

        # Deduplicate by email
        seen_emails = set()
        unique_contacts = []

        for contact in contacts:
            email = contact.get('email', '').lower()
            if email and email not in seen_emails:
                seen_emails.add(email)
                unique_contacts.append(contact)

        # Sort by multiple criteria
        unique_contacts.sort(key=lambda x: (
            -x.get('confidence', 0),                    # Higher confidence first
            bool(x.get('linkedin_url')),                # LinkedIn profiles preferred
            bool(x.get('phone')),                       # Phone numbers preferred
            'Manager' in x.get('position', ''),         # Managers preferred
            'Director' in x.get('position', ''),        # Directors preferred
            'Senior' in x.get('position', ''),          # Senior roles preferred
            len(x.get('employment_history', [])),       # More experience preferred
        ), reverse=True)

        return unique_contacts

    def get_apollo_credits_info(self) -> Dict:
        """Get Apollo API credits information"""
        try:
            url = f"{self.base_url}/auth/health"
            response = requests.get(url, headers=self.headers, timeout=10)

            if response.status_code == 200:
                data = response.json()
                return {
                    'credits_remaining': data.get('credits_remaining', 'Unknown'),
                    'credits_limit': data.get('credits_limit', 'Unknown'),
                    'plan': data.get('plan', 'Unknown'),
                    'status': 'Active'
                }
            else:
                return {'status': 'Error', 'message': 'Could not fetch credits info'}

        except Exception as e:
            return {'status': 'Error', 'message': str(e)}
