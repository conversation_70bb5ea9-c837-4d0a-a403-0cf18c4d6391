import requests
import re
from typing import List, Dict, Optional
from config import Config

class CompanySpecificHRFinder:
    """
    Company-specific HR finder with targeted strategies for major companies
    Contains specialized search patterns for different company types and industries
    """
    
    def __init__(self):
        self.apollo_api_key = Config.APOLLO_API_KEY
        
        # Company-specific HR contact patterns
        self.company_patterns = {
            # Tech Giants
            'google': {
                'hr_emails': ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
                'hr_titles': ['People Operations', 'Talent Acquisition', 'University Recruiter'],
                'linkedin_search': 'Google "People Operations" OR "Talent Acquisition" India',
                'specific_domains': ['google.com', 'alphabet.com'],
                'industry': 'Technology'
            },
            'microsoft': {
                'hr_emails': ['<EMAIL>', '<EMAIL>'],
                'hr_titles': ['Talent Acquisition', 'University Recruiter', 'HR Business Partner'],
                'linkedin_search': 'Microsoft "Talent Acquisition" OR "University Recruiter" India',
                'specific_domains': ['microsoft.com'],
                'industry': 'Technology'
            },
            'amazon': {
                'hr_emails': ['<EMAIL>', '<EMAIL>'],
                'hr_titles': ['University Recruiter', 'Talent Acquisition', 'HR Partner'],
                'linkedin_search': 'Amazon "University Recruiter" OR "Talent Acquisition" India',
                'specific_domains': ['amazon.com', 'amazon.in'],
                'industry': 'Technology'
            },
            'meta': {
                'hr_emails': ['<EMAIL>', '<EMAIL>'],
                'hr_titles': ['University Recruiter', 'Talent Partner', 'People Partner'],
                'linkedin_search': 'Meta "University Recruiter" OR "Talent Partner" India',
                'specific_domains': ['meta.com', 'facebook.com'],
                'industry': 'Technology'
            },
            'apple': {
                'hr_emails': ['<EMAIL>', '<EMAIL>'],
                'hr_titles': ['University Recruiter', 'Talent Acquisition', 'People Team'],
                'linkedin_search': 'Apple "University Recruiter" OR "Talent Acquisition" India',
                'specific_domains': ['apple.com'],
                'industry': 'Technology'
            },
            
            # Indian IT Companies
            'tcs': {
                'hr_emails': ['<EMAIL>', '<EMAIL>'],
                'hr_titles': ['Campus Recruiter', 'Talent Acquisition', 'HR Manager'],
                'linkedin_search': 'TCS "Campus Recruiter" OR "Talent Acquisition" India',
                'specific_domains': ['tcs.com'],
                'industry': 'IT Services'
            },
            'infosys': {
                'hr_emails': ['<EMAIL>', '<EMAIL>'],
                'hr_titles': ['Campus Recruiter', 'Talent Acquisition', 'HR Business Partner'],
                'linkedin_search': 'Infosys "Campus Recruiter" OR "Talent Acquisition" India',
                'specific_domains': ['infosys.com'],
                'industry': 'IT Services'
            },
            'wipro': {
                'hr_emails': ['<EMAIL>', '<EMAIL>'],
                'hr_titles': ['Campus Recruiter', 'Talent Acquisition', 'HR Manager'],
                'linkedin_search': 'Wipro "Campus Recruiter" OR "Talent Acquisition" India',
                'specific_domains': ['wipro.com'],
                'industry': 'IT Services'
            },
            'hcl': {
                'hr_emails': ['<EMAIL>', '<EMAIL>'],
                'hr_titles': ['Campus Recruiter', 'Talent Acquisition', 'HR Partner'],
                'linkedin_search': 'HCL "Campus Recruiter" OR "Talent Acquisition" India',
                'specific_domains': ['hcl.com', 'hcltech.com'],
                'industry': 'IT Services'
            },
            
            # Consulting
            'deloitte': {
                'hr_emails': ['<EMAIL>', '<EMAIL>'],
                'hr_titles': ['Campus Recruiter', 'Talent Acquisition', 'HR Consultant'],
                'linkedin_search': 'Deloitte "Campus Recruiter" OR "Talent Acquisition" India',
                'specific_domains': ['deloitte.com'],
                'industry': 'Consulting'
            },
            'accenture': {
                'hr_emails': ['<EMAIL>', '<EMAIL>'],
                'hr_titles': ['Campus Recruiter', 'Talent Acquisition', 'HR Partner'],
                'linkedin_search': 'Accenture "Campus Recruiter" OR "Talent Acquisition" India',
                'specific_domains': ['accenture.com'],
                'industry': 'Consulting'
            },
            
            # Financial Services
            'jpmorgan': {
                'hr_emails': ['<EMAIL>', '<EMAIL>'],
                'hr_titles': ['Campus Recruiter', 'Talent Acquisition', 'HR Business Partner'],
                'linkedin_search': 'JPMorgan "Campus Recruiter" OR "Talent Acquisition" India',
                'specific_domains': ['jpmorgan.com', 'jpmorganchase.com'],
                'industry': 'Financial Services'
            },
            'goldman': {
                'hr_emails': ['<EMAIL>', '<EMAIL>'],
                'hr_titles': ['Campus Recruiter', 'Talent Acquisition', 'HR Partner'],
                'linkedin_search': 'Goldman Sachs "Campus Recruiter" OR "Talent Acquisition" India',
                'specific_domains': ['gs.com', 'goldmansachs.com'],
                'industry': 'Financial Services'
            }
        }
        
        # Industry-specific patterns
        self.industry_patterns = {
            'Technology': {
                'common_hr_titles': ['Software Engineer Recruiter', 'Technical Recruiter', 'Engineering Talent Partner'],
                'search_keywords': ['tech recruiting', 'software engineer hiring', 'technical talent'],
                'email_patterns': ['tech-careers@', 'engineering-jobs@', 'developer-jobs@']
            },
            'IT Services': {
                'common_hr_titles': ['Campus Recruiter', 'Lateral Recruiter', 'IT Recruiter'],
                'search_keywords': ['campus hiring', 'lateral hiring', 'IT recruitment'],
                'email_patterns': ['campus@', 'lateral@', 'it-careers@']
            },
            'Consulting': {
                'common_hr_titles': ['Management Consultant Recruiter', 'Strategy Recruiter', 'Business Analyst Recruiter'],
                'search_keywords': ['consulting recruitment', 'strategy hiring', 'business analyst'],
                'email_patterns': ['consulting-careers@', 'strategy-jobs@', 'analyst-hiring@']
            },
            'Financial Services': {
                'common_hr_titles': ['Finance Recruiter', 'Investment Banking Recruiter', 'Analyst Recruiter'],
                'search_keywords': ['finance recruitment', 'banking careers', 'analyst hiring'],
                'email_patterns': ['finance-careers@', 'banking-jobs@', 'analyst-recruitment@']
            }
        }
    
    def find_company_specific_hr(self, company_name: str, company_domain: str = None) -> List[Dict]:
        """
        Find HR contacts using company-specific strategies
        """
        print(f"\n🎯 Company-Specific Search: Analyzing {company_name}")
        
        results = []
        
        # Normalize company name for lookup
        normalized_name = self._normalize_company_name(company_name)
        
        # Check if we have specific patterns for this company
        if normalized_name in self.company_patterns:
            print(f"  ✅ Found specific patterns for {company_name}")
            company_data = self.company_patterns[normalized_name]
            
            # Use company-specific search
            specific_results = self._search_with_company_patterns(company_name, company_domain, company_data)
            results.extend(specific_results)
        else:
            print(f"  ℹ️ No specific patterns for {company_name}, using industry-based search")
            
            # Try to identify industry and use industry patterns
            industry = self._identify_industry(company_name, company_domain)
            if industry:
                print(f"  📊 Identified industry: {industry}")
                industry_results = self._search_with_industry_patterns(company_name, company_domain, industry)
                results.extend(industry_results)
        
        # Add generic high-value search patterns
        generic_results = self._search_with_generic_patterns(company_name, company_domain)
        results.extend(generic_results)
        
        # Deduplicate and rank
        final_results = self._deduplicate_and_rank_specific(results)
        
        print(f"  📊 Company-Specific Summary: {len(results)} raw → {len(final_results)} final results")
        
        return final_results
    
    def _normalize_company_name(self, company_name: str) -> str:
        """Normalize company name for pattern matching"""
        name = company_name.lower().strip()
        
        # Handle common variations
        name_mappings = {
            'tata consultancy services': 'tcs',
            'facebook': 'meta',
            'jp morgan': 'jpmorgan',
            'jpmorgan chase': 'jpmorgan',
            'goldman sachs': 'goldman',
            'hcl technologies': 'hcl',
            'microsoft corporation': 'microsoft',
            'alphabet inc': 'google',
            'amazon.com': 'amazon'
        }
        
        return name_mappings.get(name, name.split()[0])  # Use first word if no mapping
    
    def _search_with_company_patterns(self, company_name: str, company_domain: str, company_data: Dict) -> List[Dict]:
        """Search using company-specific patterns"""
        results = []
        
        # 1. Use known HR emails
        for email in company_data.get('hr_emails', []):
            results.append({
                'name': 'HR Team',
                'email': email,
                'position': 'HR Department',
                'company': company_name,
                'source': 'Company-Specific Email Pattern',
                'confidence': 85,
                'location': 'India',
                'verified': False,
                'contact_method': 'Email',
                'api_source': 'company_specific'
            })
        
        # 2. Generate domain-specific emails if we have the domain
        if company_domain:
            domain_emails = self._generate_domain_specific_emails(company_domain, company_data)
            results.extend(domain_emails)
        
        # 3. Use specific HR titles for targeted search
        title_results = self._search_by_specific_titles(company_name, company_data.get('hr_titles', []))
        results.extend(title_results)
        
        return results
    
    def _search_with_industry_patterns(self, company_name: str, company_domain: str, industry: str) -> List[Dict]:
        """Search using industry-specific patterns"""
        results = []
        
        if industry not in self.industry_patterns:
            return results
        
        industry_data = self.industry_patterns[industry]
        
        # Generate industry-specific emails
        if company_domain:
            for pattern in industry_data.get('email_patterns', []):
                email = f"{pattern}{company_domain}"
                results.append({
                    'name': 'HR Team',
                    'email': email,
                    'position': f'{industry} HR',
                    'company': company_name,
                    'source': f'Industry-Specific Pattern ({industry})',
                    'confidence': 70,
                    'location': 'India',
                    'verified': False,
                    'contact_method': 'Email',
                    'api_source': 'industry_specific'
                })
        
        # Search by industry-specific titles
        title_results = self._search_by_specific_titles(company_name, industry_data.get('common_hr_titles', []))
        results.extend(title_results)
        
        return results
    
    def _search_with_generic_patterns(self, company_name: str, company_domain: str) -> List[Dict]:
        """Search using generic high-value patterns"""
        results = []
        
        if not company_domain:
            return results
        
        # High-value generic patterns
        generic_patterns = [
            'careers@', 'jobs@', 'hr@', 'recruitment@', 'talent@',
            'campus@', 'university@', 'hiring@', 'people@'
        ]
        
        for pattern in generic_patterns:
            email = f"{pattern}{company_domain}"
            results.append({
                'name': 'HR Department',
                'email': email,
                'position': 'HR Team',
                'company': company_name,
                'source': 'Generic HR Pattern',
                'confidence': 60,
                'location': 'India',
                'verified': False,
                'contact_method': 'Email',
                'api_source': 'generic_pattern'
            })
        
        return results
    
    def _generate_domain_specific_emails(self, domain: str, company_data: Dict) -> List[Dict]:
        """Generate domain-specific email patterns"""
        results = []
        
        # Use company-specific email patterns
        base_patterns = ['careers@', 'jobs@', 'hr@', 'talent@']
        
        # Add industry-specific patterns
        industry = company_data.get('industry', '')
        if industry == 'Technology':
            base_patterns.extend(['tech-careers@', 'engineering@', 'developers@'])
        elif industry == 'IT Services':
            base_patterns.extend(['campus@', 'lateral@', 'it-careers@'])
        elif industry == 'Consulting':
            base_patterns.extend(['consulting@', 'strategy@', 'analyst@'])
        elif industry == 'Financial Services':
            base_patterns.extend(['finance@', 'banking@', 'investment@'])
        
        for pattern in base_patterns:
            email = f"{pattern}{domain}"
            results.append({
                'name': 'HR Team',
                'email': email,
                'position': 'HR Department',
                'company': domain.replace('.com', '').title(),
                'source': 'Domain-Specific Pattern',
                'confidence': 75,
                'location': 'India',
                'verified': False,
                'contact_method': 'Email',
                'api_source': 'domain_specific'
            })
        
        return results
    
    def _search_by_specific_titles(self, company_name: str, titles: List[str]) -> List[Dict]:
        """Search for people with specific HR titles (placeholder for actual implementation)"""
        results = []
        
        # This would integrate with LinkedIn API, Apollo, or other people search APIs
        # For now, we'll create placeholder contacts based on common patterns
        
        for title in titles[:5]:  # Limit to avoid too many results
            # Generate realistic contact based on title
            results.append({
                'name': f'{title} Contact',
                'email': f'{title.lower().replace(" ", ".")}@{company_name.lower().replace(" ", "")}.com',
                'position': title,
                'company': company_name,
                'source': 'Title-Based Search',
                'confidence': 65,
                'location': 'India',
                'verified': False,
                'contact_method': 'Email',
                'api_source': 'title_search'
            })
        
        return results
    
    def _identify_industry(self, company_name: str, company_domain: str = None) -> Optional[str]:
        """Identify company industry based on name and domain"""
        name_lower = company_name.lower()
        
        # Technology keywords
        tech_keywords = ['tech', 'software', 'systems', 'solutions', 'digital', 'data', 'cloud', 'ai', 'ml']
        if any(keyword in name_lower for keyword in tech_keywords):
            return 'Technology'
        
        # IT Services keywords
        it_keywords = ['services', 'consultancy', 'consulting', 'technologies', 'infotech']
        if any(keyword in name_lower for keyword in it_keywords):
            return 'IT Services'
        
        # Financial Services keywords
        finance_keywords = ['bank', 'financial', 'capital', 'investment', 'securities', 'fund']
        if any(keyword in name_lower for keyword in finance_keywords):
            return 'Financial Services'
        
        # Consulting keywords
        consulting_keywords = ['consulting', 'advisory', 'strategy', 'management']
        if any(keyword in name_lower for keyword in consulting_keywords):
            return 'Consulting'
        
        return None
    
    def _deduplicate_and_rank_specific(self, results: List[Dict]) -> List[Dict]:
        """Deduplicate and rank company-specific results"""
        if not results:
            return []
        
        # Deduplicate by email
        seen_emails = set()
        unique_results = []
        
        for result in results:
            email = result.get('email', '').lower()
            if email and email not in seen_emails:
                seen_emails.add(email)
                unique_results.append(result)
        
        # Sort by confidence and source quality
        unique_results.sort(key=lambda x: (
            -x.get('confidence', 0),
            'Company-Specific' in x.get('source', ''),
            'Domain-Specific' in x.get('source', ''),
            'Industry-Specific' in x.get('source', ''),
            'careers@' in x.get('email', ''),
            'hr@' in x.get('email', '')
        ), reverse=True)
        
        return unique_results[:10]  # Return top 10 results
