import requests
import re
import time
import json
from typing import List, Dict, Optional
from config import Config
from urllib.parse import quote_plus
import random
from .apollo_enhanced import ApolloEnhanced
from .company_specific_finder import CompanySpecificHRFinder

class EnhancedHRFinder:
    """
    Enhanced HR finder with multiple search APIs and intelligent fallbacks
    Focuses on finding real HR professionals with verified contact information
    """
    
    def __init__(self):
        self.serp_api_key = Config.SERP_API_KEY
        self.google_api_key = Config.GOOGLE_API_KEY
        self.google_cse_id = Config.GOOGLE_CSE_ID
        self.bing_api_key = Config.BING_SEARCH_API_KEY
        self.rapidapi_key = Config.RAPIDAPI_KEY
        self.apollo_api_key = Config.APOLLO_API_KEY
        
        # API endpoints
        self.serp_url = "https://serpapi.com/search"
        self.google_url = "https://www.googleapis.com/customsearch/v1"
        self.bing_url = "https://api.bing.microsoft.com/v7.0/search"
        self.apollo_people_url = "https://api.apollo.io/api/v1/people/search"
        
        # Initialize enhanced services
        self.apollo_enhanced = ApolloEnhanced()
        self.company_specific_finder = CompanySpecificHRFinder()

        # Search strategies in order of preference
        self.search_strategies = [
            ('Company-Specific Search', self._company_specific_search),
            ('Apollo Enhanced Search', self._apollo_enhanced_search),
            ('Apollo People Search', self._apollo_people_search),
            ('SERP API LinkedIn', self._serp_linkedin_search),
            ('Google Custom Search', self._google_custom_search),
            ('Bing Search API', self._bing_search),
            ('Company Website Scraping', self._company_website_search),
            ('Email Pattern Generation', self._generate_email_patterns)
        ]
    
    def find_hr_professionals(self, company_name: str, company_domain: str = None) -> List[Dict]:
        """
        Find HR professionals using multiple search strategies
        Returns a list of HR contacts with confidence scores
        """
        print(f"\n🔍 Enhanced HR Search: Finding HR professionals at {company_name}")
        
        all_results = []
        successful_strategies = 0
        
        for strategy_name, strategy_func in self.search_strategies:
            try:
                print(f"  📋 Trying {strategy_name}...")
                results = strategy_func(company_name, company_domain)
                
                if results:
                    print(f"    ✅ Found {len(results)} contacts via {strategy_name}")
                    all_results.extend(results)
                    successful_strategies += 1
                else:
                    print(f"    ❌ No results from {strategy_name}")
                
                # Rate limiting between strategies
                time.sleep(1)
                
                # Stop if we have enough good results
                if len(all_results) >= 20 and successful_strategies >= 3:
                    break
                    
            except Exception as e:
                print(f"    ⚠️ Error in {strategy_name}: {e}")
                continue
        
        # Deduplicate and rank results
        unique_results = self._deduplicate_and_rank(all_results)
        
        print(f"\n📊 Search Summary:")
        print(f"  Total strategies tried: {len(self.search_strategies)}")
        print(f"  Successful strategies: {successful_strategies}")
        print(f"  Raw results found: {len(all_results)}")
        print(f"  Unique results after deduplication: {len(unique_results)}")
        
        return unique_results[:15]  # Return top 15 results

    def _company_specific_search(self, company_name: str, company_domain: str = None) -> List[Dict]:
        """Use company-specific search patterns"""
        try:
            return self.company_specific_finder.find_company_specific_hr(company_name, company_domain)
        except Exception as e:
            print(f"      Company-specific search error: {e}")
            return []

    def _apollo_enhanced_search(self, company_name: str, company_domain: str = None) -> List[Dict]:
        """Use enhanced Apollo search with advanced filtering"""
        try:
            return self.apollo_enhanced.find_hr_professionals_advanced(company_name, company_domain)
        except Exception as e:
            print(f"      Apollo Enhanced search error: {e}")
            return []

    def _apollo_people_search(self, company_name: str, company_domain: str = None) -> List[Dict]:
        """Search for HR people using Apollo.io People Search API"""
        if not self.apollo_api_key:
            return []
        
        results = []
        
        # HR-related job titles to search for
        hr_titles = [
            "HR Manager", "Human Resources Manager", "HR Director", 
            "Talent Acquisition", "Recruiter", "HR Business Partner",
            "People Operations", "HR Generalist", "Talent Manager",
            "Chief People Officer", "VP Human Resources"
        ]
        
        headers = {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache',
            'X-Api-Key': self.apollo_api_key
        }
        
        for title in hr_titles[:5]:  # Limit to avoid quota exhaustion
            try:
                search_params = {
                    "person_titles": [title],
                    "organization_names": [company_name],
                    "per_page": 10
                }
                
                if company_domain:
                    search_params["organization_domains"] = [company_domain]
                
                response = requests.post(
                    self.apollo_people_url, 
                    headers=headers, 
                    json=search_params,
                    timeout=30
                )
                
                if response.status_code == 200:
                    data = response.json()
                    people = data.get('people', [])
                    
                    for person in people:
                        contact = self._extract_apollo_person_data(person, company_name)
                        if contact:
                            results.append(contact)
                
                time.sleep(0.5)  # Rate limiting
                
            except Exception as e:
                print(f"      Apollo search error for {title}: {e}")
                continue
        
        return results
    
    def _extract_apollo_person_data(self, person: Dict, company_name: str) -> Optional[Dict]:
        """Extract and format person data from Apollo API response"""
        try:
            name = f"{person.get('first_name', '')} {person.get('last_name', '')}".strip()
            email = person.get('email')
            title = person.get('title', '')
            linkedin_url = person.get('linkedin_url', '')
            
            if not name or not email:
                return None
            
            # Get organization info
            organization = person.get('organization', {})
            org_name = organization.get('name', company_name)
            
            return {
                'name': name,
                'email': email,
                'position': title,
                'company': org_name,
                'linkedin_url': linkedin_url,
                'source': 'Apollo People Search',
                'confidence': 95,  # High confidence for Apollo data
                'location': 'India' if 'india' in title.lower() or 'india' in org_name.lower() else 'Unknown',
                'verified': True,
                'contact_method': 'Email',
                'api_source': 'apollo'
            }
            
        except Exception as e:
            print(f"      Error extracting Apollo person data: {e}")
            return None
    
    def _serp_linkedin_search(self, company_name: str, company_domain: str = None) -> List[Dict]:
        """Search LinkedIn using SERP API with improved queries"""
        if not self.serp_api_key:
            return []
        
        results = []
        
        # Enhanced LinkedIn search queries
        linkedin_queries = [
            f'"{company_name}" "HR Manager" OR "Human Resources" India site:linkedin.com/in',
            f'"{company_name}" "Talent Acquisition" OR "Recruiter" India site:linkedin.com/in',
            f'"{company_name}" "People Operations" OR "HR Business Partner" site:linkedin.com/in',
            f'"HR at {company_name}" India site:linkedin.com/in',
            f'"Human Resources {company_name}" India site:linkedin.com/in'
        ]
        
        for query in linkedin_queries[:3]:  # Limit queries to avoid quota
            try:
                params = {
                    'api_key': self.serp_api_key,
                    'engine': 'google',
                    'q': query,
                    'num': 10,
                    'gl': 'in',
                    'hl': 'en'
                }
                
                response = requests.get(self.serp_url, params=params, timeout=30)
                
                if response.status_code == 200:
                    data = response.json()
                    organic_results = data.get('organic_results', [])
                    
                    for result in organic_results:
                        contact = self._extract_linkedin_contact(result, company_name)
                        if contact:
                            results.append(contact)
                elif response.status_code == 429:
                    print("      SERP API quota exhausted, skipping remaining queries")
                    break
                
                time.sleep(1)
                
            except Exception as e:
                print(f"      SERP LinkedIn search error: {e}")
                continue
        
        return results
    
    def _extract_linkedin_contact(self, result: Dict, company_name: str) -> Optional[Dict]:
        """Extract contact information from LinkedIn search result"""
        try:
            title = result.get('title', '')
            snippet = result.get('snippet', '')
            link = result.get('link', '')
            
            # Extract name from LinkedIn URL or title
            name = self._extract_name_from_linkedin(title, link)
            if not name:
                return None
            
            # Extract position from title or snippet
            position = self._extract_position_from_text(f"{title} {snippet}")
            
            # Generate email based on name and company domain
            email = self._generate_email_from_name(name, company_name)
            
            return {
                'name': name,
                'email': email,
                'position': position,
                'company': company_name,
                'linkedin_url': link,
                'source': 'SERP API - LinkedIn',
                'confidence': 85,
                'location': 'India',
                'verified': False,
                'contact_method': 'LinkedIn + Email',
                'api_source': 'serp'
            }
            
        except Exception as e:
            print(f"      Error extracting LinkedIn contact: {e}")
            return None
    
    def _google_custom_search(self, company_name: str, company_domain: str = None) -> List[Dict]:
        """Search using Google Custom Search API"""
        if not self.google_api_key or not self.google_cse_id:
            return []
        
        results = []
        
        search_queries = [
            f'"{company_name}" HR contact email India',
            f'"{company_name}" "Human Resources" contact India',
            f'site:linkedin.com "{company_name}" HR Manager India',
            f'"{company_name}" "Talent Acquisition" email India'
        ]
        
        for query in search_queries[:2]:  # Limit to avoid quota
            try:
                params = {
                    'key': self.google_api_key,
                    'cx': self.google_cse_id,
                    'q': query,
                    'num': 10
                }
                
                response = requests.get(self.google_url, params=params, timeout=30)
                
                if response.status_code == 200:
                    data = response.json()
                    items = data.get('items', [])
                    
                    for item in items:
                        contact = self._extract_google_contact(item, company_name)
                        if contact:
                            results.append(contact)
                
                time.sleep(1)
                
            except Exception as e:
                print(f"      Google Custom Search error: {e}")
                continue
        
        return results

    def _company_website_search(self, company_name: str, company_domain: str = None) -> List[Dict]:
        """Search company website for HR contacts"""
        if not company_domain:
            return []

        results = []

        # Common HR page URLs
        hr_pages = [
            f"https://{company_domain}/careers",
            f"https://{company_domain}/jobs",
            f"https://{company_domain}/about/team",
            f"https://{company_domain}/contact",
            f"https://{company_domain}/hr"
        ]

        for url in hr_pages:
            try:
                response = requests.get(url, timeout=10, headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                })

                if response.status_code == 200:
                    contacts = self._extract_contacts_from_html(response.text, company_name, company_domain)
                    results.extend(contacts)

                time.sleep(1)

            except Exception as e:
                print(f"      Website search error for {url}: {e}")
                continue

        return results

    def _generate_email_patterns(self, company_name: str, company_domain: str = None) -> List[Dict]:
        """Generate common HR email patterns"""
        if not company_domain:
            return []

        results = []

        # Common HR email patterns
        hr_patterns = [
            f"hr@{company_domain}",
            f"careers@{company_domain}",
            f"jobs@{company_domain}",
            f"recruitment@{company_domain}",
            f"talent@{company_domain}",
            f"people@{company_domain}",
            f"humanresources@{company_domain}",
            f"hiring@{company_domain}"
        ]

        for email in hr_patterns:
            results.append({
                'name': 'HR Department',
                'email': email,
                'position': 'HR Team',
                'company': company_name,
                'source': 'Email Pattern Generation',
                'confidence': 60,
                'location': 'India',
                'verified': False,
                'contact_method': 'Email',
                'api_source': 'pattern'
            })

        return results

    def _extract_name_from_linkedin(self, title: str, url: str) -> Optional[str]:
        """Extract person name from LinkedIn title or URL"""
        try:
            # Try to extract from URL first
            if '/in/' in url:
                profile_part = url.split('/in/')[-1].split('/')[0]
                # Convert linkedin-style-name to proper name
                name_parts = profile_part.replace('-', ' ').split()
                if len(name_parts) >= 2:
                    return ' '.join(word.capitalize() for word in name_parts[:2])

            # Try to extract from title
            # Look for patterns like "Name - Position at Company"
            if ' - ' in title:
                potential_name = title.split(' - ')[0].strip()
                if len(potential_name.split()) <= 3 and not any(word.lower() in potential_name.lower()
                    for word in ['linkedin', 'profile', 'manager', 'director']):
                    return potential_name

            return None

        except Exception:
            return None

    def _extract_position_from_text(self, text: str) -> str:
        """Extract HR position from text"""
        hr_positions = [
            'HR Manager', 'Human Resources Manager', 'HR Director',
            'Talent Acquisition Manager', 'Recruiter', 'HR Business Partner',
            'People Operations Manager', 'HR Generalist', 'Talent Manager',
            'Chief People Officer', 'VP Human Resources', 'Head of HR'
        ]

        text_lower = text.lower()
        for position in hr_positions:
            if position.lower() in text_lower:
                return position

        # Fallback patterns
        if 'hr' in text_lower or 'human resources' in text_lower:
            return 'HR Professional'
        elif 'talent' in text_lower or 'recruitment' in text_lower:
            return 'Talent Acquisition'
        else:
            return 'HR Team Member'

    def _generate_email_from_name(self, name: str, company_name: str) -> str:
        """Generate email address from person name"""
        # This is a placeholder - in production, you'd use the company domain
        # For now, we'll create a pattern-based email
        name_parts = name.lower().split()
        if len(name_parts) >= 2:
            first_name = name_parts[0]
            last_name = name_parts[-1]

            # Try to get company domain from company name
            domain = f"{company_name.lower().replace(' ', '').replace('.', '')}.com"

            # Common email patterns
            patterns = [
                f"{first_name}.{last_name}@{domain}",
                f"{first_name}@{domain}",
                f"{first_name[0]}{last_name}@{domain}"
            ]

            return patterns[0]  # Return the most common pattern

        return f"hr@{company_name.lower().replace(' ', '')}.com"

    def _extract_google_contact(self, item: Dict, company_name: str) -> Optional[Dict]:
        """Extract contact from Google search result"""
        try:
            title = item.get('title', '')
            snippet = item.get('snippet', '')
            link = item.get('link', '')

            # Look for email addresses in snippet
            emails = re.findall(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', snippet)

            if emails and any(hr_word in f"{title} {snippet}".lower()
                            for hr_word in ['hr', 'human resources', 'talent', 'recruiter']):

                return {
                    'name': 'HR Contact',
                    'email': emails[0],
                    'position': self._extract_position_from_text(f"{title} {snippet}"),
                    'company': company_name,
                    'source': 'Google Custom Search',
                    'confidence': 75,
                    'location': 'India',
                    'verified': False,
                    'contact_method': 'Email',
                    'api_source': 'google'
                }

            return None

        except Exception as e:
            print(f"      Error extracting Google contact: {e}")
            return None

    def _extract_bing_contact(self, page: Dict, company_name: str) -> Optional[Dict]:
        """Extract contact from Bing search result"""
        try:
            name = page.get('name', '')
            snippet = page.get('snippet', '')
            url = page.get('url', '')

            # Similar logic to Google extraction
            emails = re.findall(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', snippet)

            if emails and any(hr_word in f"{name} {snippet}".lower()
                            for hr_word in ['hr', 'human resources', 'talent', 'recruiter']):

                return {
                    'name': 'HR Contact',
                    'email': emails[0],
                    'position': self._extract_position_from_text(f"{name} {snippet}"),
                    'company': company_name,
                    'source': 'Bing Search',
                    'confidence': 70,
                    'location': 'India',
                    'verified': False,
                    'contact_method': 'Email',
                    'api_source': 'bing'
                }

            return None

        except Exception as e:
            print(f"      Error extracting Bing contact: {e}")
            return None

    def _extract_contacts_from_html(self, html: str, company_name: str, company_domain: str) -> List[Dict]:
        """Extract contacts from HTML content"""
        results = []

        try:
            # Find email addresses
            emails = re.findall(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', html)

            # Filter for company domain emails
            company_emails = [email for email in emails if company_domain in email.lower()]

            # Look for HR-related emails
            hr_emails = [email for email in company_emails
                        if any(hr_word in email.lower() for hr_word in ['hr', 'careers', 'jobs', 'talent', 'recruitment'])]

            for email in hr_emails[:5]:  # Limit results
                results.append({
                    'name': 'HR Department',
                    'email': email,
                    'position': 'HR Team',
                    'company': company_name,
                    'source': 'Company Website',
                    'confidence': 80,
                    'location': 'India',
                    'verified': False,
                    'contact_method': 'Email',
                    'api_source': 'website'
                })

        except Exception as e:
            print(f"      Error extracting contacts from HTML: {e}")

        return results

    def _deduplicate_and_rank(self, results: List[Dict]) -> List[Dict]:
        """Remove duplicates and rank results by confidence and quality"""
        if not results:
            return []

        # Deduplicate by email address
        seen_emails = set()
        unique_results = []

        for result in results:
            email = result.get('email', '').lower()
            if email and email not in seen_emails:
                seen_emails.add(email)
                unique_results.append(result)

        # Sort by multiple criteria
        unique_results.sort(key=lambda x: (
            -x.get('confidence', 0),           # Higher confidence first
            x.get('verified', False),          # Verified contacts first
            'Apollo' in x.get('source', ''),   # Apollo results preferred
            'LinkedIn' in x.get('source', ''), # LinkedIn results preferred
            x.get('location') == 'India',      # India-based preferred
            '@' in x.get('email', '')          # Valid email format
        ), reverse=True)

        return unique_results
    
    def _bing_search(self, company_name: str, company_domain: str = None) -> List[Dict]:
        """Search using Bing Search API"""
        if not self.bing_api_key:
            return []
        
        results = []
        
        search_queries = [
            f'"{company_name}" HR Manager India site:linkedin.com',
            f'"{company_name}" Human Resources contact India'
        ]
        
        headers = {
            'Ocp-Apim-Subscription-Key': self.bing_api_key
        }
        
        for query in search_queries:
            try:
                params = {
                    'q': query,
                    'count': 10,
                    'mkt': 'en-IN'
                }
                
                response = requests.get(self.bing_url, headers=headers, params=params, timeout=30)
                
                if response.status_code == 200:
                    data = response.json()
                    web_pages = data.get('webPages', {}).get('value', [])
                    
                    for page in web_pages:
                        contact = self._extract_bing_contact(page, company_name)
                        if contact:
                            results.append(contact)
                
                time.sleep(1)
                
            except Exception as e:
                print(f"      Bing Search error: {e}")
                continue
        
        return results
