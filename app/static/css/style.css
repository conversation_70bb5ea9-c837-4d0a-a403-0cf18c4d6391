/* Custom CSS for JobSearch App */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* Navigation */
.navbar-brand {
    font-size: 1.5rem;
}

/* Cards */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: var(--light-color);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-weight: 600;
}

/* Buttons */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Forms */
.form-control, .form-select {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Dashboard Stats */
.stat-card {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    border-radius: 1rem;
}

.stat-card .stat-number {
    font-size: 2.5rem;
    font-weight: 700;
}

.stat-card .stat-label {
    font-size: 0.875rem;
    opacity: 0.9;
}

/* Job Search Results */
.job-search-card {
    border-left: 4px solid var(--primary-color);
}

.job-search-card.contacted {
    border-left-color: var(--success-color);
}

.job-search-card.responded {
    border-left-color: var(--info-color);
}

.job-search-card.rejected {
    border-left-color: var(--danger-color);
}

/* Email Templates */
.template-preview {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    font-family: monospace;
    font-size: 0.875rem;
}

/* SMTP Config Status */
.smtp-status {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.smtp-status.verified {
    color: var(--success-color);
}

.smtp-status.unverified {
    color: var(--warning-color);
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .stat-card .stat-number {
        font-size: 2rem;
    }
    
    .card-body {
        padding: 1rem;
    }
}

/* Animation */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* Custom Utilities */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.border-start-primary {
    border-left: 4px solid var(--primary-color) !important;
}

.border-start-success {
    border-left: 4px solid var(--success-color) !important;
}

.border-start-warning {
    border-left: 4px solid var(--warning-color) !important;
}

.border-start-danger {
    border-left: 4px solid var(--danger-color) !important;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    padding: 4rem 0;
}

.hero-section h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.hero-section p {
    font-size: 1.25rem;
    opacity: 0.9;
}

/* Feature Cards */
.feature-card {
    text-align: center;
    padding: 2rem;
    height: 100%;
}

.feature-card .feature-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

/* Email Preview */
.email-preview {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin: 1rem 0;
}

.email-preview .email-header {
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 1rem;
    margin-bottom: 1rem;
}

.email-preview .email-subject {
    font-weight: 600;
    font-size: 1.1rem;
}

.email-preview .email-meta {
    font-size: 0.875rem;
    color: var(--secondary-color);
}

.email-preview .email-body {
    line-height: 1.6;
}

/* Hiring Managers Page */
.hiring-manager-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.hiring-manager-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.75rem 1.5rem rgba(0, 0, 0, 0.15);
}

.hiring-manager-card .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    padding: 1rem;
}

.hiring-manager-card .card-title {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.hiring-manager-card .card-body {
    padding: 1rem;
}

.hiring-manager-card .card-footer {
    padding: 0.75rem 1rem;
    background-color: transparent;
    border-top: 1px solid rgba(0, 0, 0, 0.125);
}

.hiring-manager-card .text-primary {
    color: var(--primary-color) !important;
    font-weight: 600;
}

.hiring-manager-card .btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

/* Loading and empty states */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

#noResults i {
    opacity: 0.5;
}

/* Toast notifications */
.toast {
    min-width: 300px;
}

.toast-header {
    font-weight: 600;
}

/* Search filters */
.card .card-header h5 {
    color: var(--dark-color);
    font-weight: 600;
}

/* Email modal */
#emailModal .modal-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0b5ed7 100%);
    color: white;
    border-bottom: none;
}

#emailModal .modal-header .btn-close {
    filter: invert(1);
}

#emailModal .modal-body {
    padding: 1.5rem;
}

#emailModal .form-label {
    font-weight: 600;
    color: var(--dark-color);
}

#emailModal textarea {
    min-height: 150px;
    resize: vertical;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .hiring-manager-card .card-header {
        padding: 0.75rem;
    }

    .hiring-manager-card .card-body {
        padding: 0.75rem;
    }

    .hiring-manager-card .card-footer {
        padding: 0.75rem;
    }

    .hiring-manager-card .d-flex.gap-2 {
        flex-direction: column;
        gap: 0.5rem !important;
    }

    .hiring-manager-card .btn-sm {
        width: 100%;
    }
}
