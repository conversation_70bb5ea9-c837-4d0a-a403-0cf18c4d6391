// Hiring Managers Page JavaScript

let currentHiringManagers = [];

// Initialize page when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    loadHiringManagers();
});

// Load hiring managers data
function loadHiringManagers(company = '', location = '', industry = '') {
    showLoading();
    hideResults();
    hideError();

    // Build query parameters
    const params = new URLSearchParams();
    if (company) params.append('company', company);
    if (location) params.append('location', location);
    if (industry) params.append('industry', industry);

    const url = `/hiring-managers/data${params.toString() ? '?' + params.toString() : ''}`;

    fetch(url)
        .then(response => response.json())
        .then(data => {
            hideLoading();
            
            if (data.success) {
                currentHiringManagers = data.data || [];
                displayHiringManagers(currentHiringManagers);
                showResultsSummary(data.count, data.search_criteria);
            } else {
                showError(data.error || 'Failed to load hiring managers');
            }
        })
        .catch(error => {
            hideLoading();
            showError('Network error: ' + error.message);
        });
}

// Display hiring managers in grid
function displayHiringManagers(hiringManagers) {
    const grid = document.getElementById('hiringManagersGrid');
    
    if (!hiringManagers || hiringManagers.length === 0) {
        grid.innerHTML = '';
        showNoResults();
        return;
    }

    grid.innerHTML = hiringManagers.map(manager => createHiringManagerCard(manager)).join('');
    showResults();
}

// Create individual hiring manager card
function createHiringManagerCard(manager) {
    const hiringManagerName = manager.ai_hiring_manager_name || manager.li_hiring_manager_name || 'Hiring Manager';
    const email = manager.ai_hiring_manager_email_address || 'No email available';
    const company = manager.organization || 'Unknown Company';
    const jobTitle = manager.title || 'Position Available';
    const location = manager.locations_derived ? manager.locations_derived.join(', ') : 'Location not specified';
    const datePosted = manager.date_posted ? new Date(manager.date_posted).toLocaleDateString() : 'Recently';
    const jobUrl = manager.url || '#';
    const linkedinUrl = manager.li_hiring_manager_url || '';
    const linkedinTitle = manager.li_hiring_manager_title || '';

    return `
        <div class="col-lg-6 col-xl-4">
            <div class="card h-100 hiring-manager-card">
                <div class="card-header d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="card-title mb-1">${hiringManagerName}</h6>
                        <small class="text-muted">${company}</small>
                    </div>
                    <div class="text-end">
                        ${linkedinUrl ? `<a href="${linkedinUrl}" target="_blank" class="btn btn-sm btn-outline-primary me-1" title="LinkedIn Profile">
                            <i class="bi bi-linkedin"></i>
                        </a>` : ''}
                        ${email !== 'No email available' ? `<button class="btn btn-sm btn-primary" onclick="openEmailModal('${email}', '${hiringManagerName}', '${company}', '${jobTitle}')" title="Send Email">
                            <i class="bi bi-envelope"></i>
                        </button>` : ''}
                    </div>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-primary mb-1">${jobTitle}</h6>
                        ${linkedinTitle ? `<small class="text-muted d-block">${linkedinTitle}</small>` : ''}
                    </div>
                    
                    <div class="mb-2">
                        <i class="bi bi-geo-alt text-muted me-1"></i>
                        <small>${location}</small>
                    </div>
                    
                    <div class="mb-2">
                        <i class="bi bi-calendar text-muted me-1"></i>
                        <small>Posted: ${datePosted}</small>
                    </div>
                    
                    <div class="mb-3">
                        <i class="bi bi-envelope text-muted me-1"></i>
                        <small class="text-break">${email}</small>
                    </div>
                </div>
                <div class="card-footer bg-transparent">
                    <div class="d-flex gap-2">
                        ${jobUrl !== '#' ? `<a href="${jobUrl}" target="_blank" class="btn btn-outline-secondary btn-sm flex-fill">
                            <i class="bi bi-link-45deg me-1"></i>View Job
                        </a>` : ''}
                        ${email !== 'No email available' ? `<button class="btn btn-primary btn-sm flex-fill" onclick="openEmailModal('${email}', '${hiringManagerName}', '${company}', '${jobTitle}')">
                            <i class="bi bi-envelope me-1"></i>Contact
                        </button>` : ''}
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Search hiring managers with filters
function searchHiringManagers() {
    const company = document.getElementById('companyFilter').value.trim();
    const location = document.getElementById('locationFilter').value.trim();
    const industry = document.getElementById('industryFilter').value;
    
    loadHiringManagers(company, location, industry);
}

// Clear all filters
function clearFilters() {
    document.getElementById('companyFilter').value = '';
    document.getElementById('locationFilter').value = '';
    document.getElementById('industryFilter').value = '';
    loadHiringManagers();
}

// Refresh data
function refreshData() {
    const company = document.getElementById('companyFilter').value.trim();
    const location = document.getElementById('locationFilter').value.trim();
    const industry = document.getElementById('industryFilter').value;
    
    loadHiringManagers(company, location, industry);
}

// Open email modal
function openEmailModal(email, hiringManagerName, company, jobTitle) {
    document.getElementById('emailTo').value = email;
    document.getElementById('emailHiringManager').value = hiringManagerName;
    document.getElementById('emailCompany').value = company;
    document.getElementById('emailJobTitle').value = jobTitle;
    
    // Generate default subject
    const subject = `Application for ${jobTitle} at ${company}`;
    document.getElementById('emailSubject').value = subject;
    
    // Generate default message template
    const message = `Dear ${hiringManagerName},

I am writing to express my interest in the ${jobTitle} position at ${company}. 

I believe my skills and experience make me a strong candidate for this role. I would welcome the opportunity to discuss how I can contribute to your team.

Please find my resume attached for your review. I look forward to hearing from you.

Best regards,
[Your Name]`;
    
    document.getElementById('emailMessage').value = message;
    
    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('emailModal'));
    modal.show();
}

// Send email
function sendEmail() {
    const emailData = {
        to_email: document.getElementById('emailTo').value,
        hiring_manager_name: document.getElementById('emailHiringManager').value,
        company: document.getElementById('emailCompany').value,
        job_title: document.getElementById('emailJobTitle').value,
        subject: document.getElementById('emailSubject').value,
        message: document.getElementById('emailMessage').value
    };
    
    // Validate required fields
    if (!emailData.to_email || !emailData.message) {
        showErrorToast('Email address and message are required');
        return;
    }
    
    // Show loading state
    const sendButton = document.querySelector('#emailModal .btn-primary');
    const originalText = sendButton.innerHTML;
    sendButton.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Sending...';
    sendButton.disabled = true;
    
    fetch('/hiring-managers/send-email', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(emailData)
    })
    .then(response => response.json())
    .then(data => {
        sendButton.innerHTML = originalText;
        sendButton.disabled = false;
        
        if (data.success) {
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('emailModal'));
            modal.hide();
            
            // Show success message
            showSuccessToast(data.message);
            
            // Clear form
            document.getElementById('emailForm').reset();
        } else {
            showErrorToast(data.error || 'Failed to send email');
            
            // If redirect is suggested (e.g., SMTP config needed)
            if (data.redirect) {
                setTimeout(() => {
                    window.location.href = data.redirect;
                }, 2000);
            }
        }
    })
    .catch(error => {
        sendButton.innerHTML = originalText;
        sendButton.disabled = false;
        showErrorToast('Network error: ' + error.message);
    });
}

// Utility functions for UI state management
function showLoading() {
    document.getElementById('loadingIndicator').style.display = 'block';
}

function hideLoading() {
    document.getElementById('loadingIndicator').style.display = 'none';
}

function showResults() {
    document.getElementById('hiringManagersGrid').style.display = 'block';
    document.getElementById('noResults').style.display = 'none';
}

function hideResults() {
    document.getElementById('hiringManagersGrid').style.display = 'none';
    document.getElementById('resultsSummary').style.display = 'none';
}

function showNoResults() {
    document.getElementById('noResults').style.display = 'block';
    document.getElementById('resultsSummary').style.display = 'none';
}

function showResultsSummary(count, criteria) {
    const summaryElement = document.getElementById('resultsSummary');
    const countElement = document.getElementById('resultsCount');
    const criteriaElement = document.getElementById('searchCriteria');
    
    countElement.textContent = count;
    
    let criteriaText = '';
    if (criteria && (criteria.company || criteria.location || criteria.industry)) {
        const parts = [];
        if (criteria.company) parts.push(`company: "${criteria.company}"`);
        if (criteria.location) parts.push(`location: "${criteria.location}"`);
        if (criteria.industry) parts.push(`industry: "${criteria.industry}"`);
        criteriaText = ` matching ${parts.join(', ')}`;
    }
    
    criteriaElement.textContent = criteriaText;
    summaryElement.style.display = 'block';
}

function showError(message) {
    const errorElement = document.getElementById('errorMessage');
    const errorText = document.getElementById('errorText');
    errorText.textContent = message;
    errorElement.style.display = 'block';
}

function hideError() {
    document.getElementById('errorMessage').style.display = 'none';
}

function showSuccessToast(message) {
    const toast = document.getElementById('successToast');
    const messageElement = document.getElementById('successMessage');
    messageElement.textContent = message;
    
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
}

function showErrorToast(message) {
    const toast = document.getElementById('errorToast');
    const messageElement = document.getElementById('errorToastMessage');
    messageElement.textContent = message;
    
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
}

// Add Enter key support for search
document.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        const activeElement = document.activeElement;
        if (activeElement && (
            activeElement.id === 'companyFilter' || 
            activeElement.id === 'locationFilter' || 
            activeElement.id === 'industryFilter'
        )) {
            searchHiringManagers();
        }
    }
});
