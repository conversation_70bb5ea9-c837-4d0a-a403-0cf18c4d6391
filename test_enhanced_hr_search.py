#!/usr/bin/env python3
"""
Test script for Enhanced HR Search functionality
Tests all the new features and API integrations
"""

import sys
import os
sys.path.append('.')

from app.services.enhanced_hr_finder import EnhancedHRFinder
from app.services.email_verifier import EmailVerifier
from app.services.apollo_enhanced import ApolloEnhanced
from app.services.company_specific_finder import CompanySpecificHR<PERSON>inder
from config import Config

def test_api_configurations():
    """Test API key configurations"""
    print("🔧 Testing API Configurations...")
    
    configs = {
        'SERP_API_KEY': Config.SERP_API_KEY,
        'APOLLO_API_KEY': Config.APOLLO_API_KEY,
        'GOOGLE_API_KEY': Config.GOOGLE_API_KEY,
        'GOOGLE_CSE_ID': Config.GOOGLE_CSE_ID,
        'BING_SEARCH_API_KEY': Config.BING_SEARCH_API_KEY,
        'HUNTER_API_KEY': Config.HUNTER_API_KEY,
        'C<PERSON>AROUT_API_KEY': Config.CLEAROUT_API_KEY,
        'ZER<PERSON>BOUNCE_API_KEY': Config.ZEROBOUNCE_API_KEY
    }
    
    for key, value in configs.items():
        status = "✅ Configured" if value else "❌ Not configured"
        print(f"  {key}: {status}")
    
    print()

def test_apollo_enhanced():
    """Test Apollo Enhanced functionality"""
    print("🚀 Testing Apollo Enhanced Search...")
    
    try:
        apollo = ApolloEnhanced()
        
        # Test with a well-known company
        results = apollo.find_hr_professionals_advanced("Google", "google.com")
        
        print(f"  Results found: {len(results)}")
        
        if results:
            print("  Sample results:")
            for i, result in enumerate(results[:3]):
                print(f"    {i+1}. {result.get('name')} - {result.get('email')} ({result.get('confidence')}%)")
        
        # Test credits info
        credits_info = apollo.get_apollo_credits_info()
        print(f"  Apollo Credits: {credits_info}")
        
    except Exception as e:
        print(f"  ❌ Apollo Enhanced error: {e}")
    
    print()

def test_company_specific_finder():
    """Test Company-Specific HR Finder"""
    print("🎯 Testing Company-Specific Search...")
    
    try:
        finder = CompanySpecificHRFinder()
        
        # Test with companies that have specific patterns
        test_companies = [
            ("Google", "google.com"),
            ("Microsoft", "microsoft.com"),
            ("TCS", "tcs.com"),
            ("Unknown Company", "unknown.com")
        ]
        
        for company_name, domain in test_companies:
            print(f"  Testing {company_name}...")
            results = finder.find_company_specific_hr(company_name, domain)
            print(f"    Found {len(results)} contacts")
            
            if results:
                sample = results[0]
                print(f"    Sample: {sample.get('email')} ({sample.get('source')})")
        
    except Exception as e:
        print(f"  ❌ Company-Specific error: {e}")
    
    print()

def test_email_verifier():
    """Test Email Verification functionality"""
    print("📧 Testing Email Verification...")
    
    try:
        verifier = EmailVerifier()
        
        # Test with sample emails
        test_emails = [
            {'email': '<EMAIL>', 'name': 'Google HR', 'confidence': 80},
            {'email': '<EMAIL>', 'name': 'Invalid', 'confidence': 50},
            {'email': '<EMAIL>', 'name': 'Microsoft HR', 'confidence': 75}
        ]
        
        print(f"  Testing {len(test_emails)} email addresses...")
        verified_contacts = verifier.verify_email_list(test_emails)
        
        print("  Verification results:")
        for contact in verified_contacts:
            status = "✅ Valid" if contact.get('is_valid') else "❌ Invalid"
            score = contact.get('verification_score', 0)
            print(f"    {contact.get('email')}: {status} (Score: {score}%)")
        
        # Get summary
        summary = verifier.get_verification_summary(verified_contacts)
        print(f"  Summary: {summary.get('valid_emails')}/{summary.get('total_emails')} valid ({summary.get('validity_rate')}%)")
        
    except Exception as e:
        print(f"  ❌ Email Verification error: {e}")
    
    print()

def test_enhanced_hr_finder():
    """Test the main Enhanced HR Finder"""
    print("⭐ Testing Enhanced HR Finder (Main System)...")
    
    try:
        finder = EnhancedHRFinder()
        
        # Test with a well-known company
        company_name = "Infosys"
        company_domain = "infosys.com"
        
        print(f"  Searching for HR contacts at {company_name}...")
        results = finder.find_hr_professionals(company_name, company_domain)
        
        print(f"  Total results: {len(results)}")
        
        if results:
            print("  Top 5 results:")
            for i, result in enumerate(results[:5]):
                confidence = result.get('confidence', 0)
                source = result.get('source', 'Unknown')
                verified = "✅" if result.get('verified') else "❓"
                print(f"    {i+1}. {result.get('name')} - {result.get('email')}")
                print(f"       {verified} {confidence}% confidence | Source: {source}")
        
        # Test source distribution
        sources = {}
        for result in results:
            source = result.get('api_source', 'unknown')
            sources[source] = sources.get(source, 0) + 1
        
        print("  Results by source:")
        for source, count in sources.items():
            print(f"    {source}: {count} contacts")
        
    except Exception as e:
        print(f"  ❌ Enhanced HR Finder error: {e}")
    
    print()

def test_api_fallbacks():
    """Test API fallback functionality"""
    print("🔄 Testing API Fallback System...")
    
    try:
        finder = EnhancedHRFinder()
        
        # Test each strategy individually
        strategies = [
            ('Company-Specific', finder._company_specific_search),
            ('Apollo Enhanced', finder._apollo_enhanced_search),
            ('SERP LinkedIn', finder._serp_linkedin_search),
            ('Google Custom', finder._google_custom_search),
            ('Bing Search', finder._bing_search),
            ('Website Scraping', finder._company_website_search),
            ('Email Patterns', finder._generate_email_patterns)
        ]
        
        company_name = "Microsoft"
        company_domain = "microsoft.com"
        
        for strategy_name, strategy_func in strategies:
            try:
                print(f"  Testing {strategy_name}...")
                results = strategy_func(company_name, company_domain)
                status = f"✅ {len(results)} results" if results else "❌ No results"
                print(f"    {status}")
            except Exception as e:
                print(f"    ❌ Error: {str(e)[:50]}...")
        
    except Exception as e:
        print(f"  ❌ Fallback test error: {e}")
    
    print()

def main():
    """Run all tests"""
    print("🧪 Enhanced HR Search System - Test Suite")
    print("=" * 50)
    
    # Run all tests
    test_api_configurations()
    test_apollo_enhanced()
    test_company_specific_finder()
    test_email_verifier()
    test_enhanced_hr_finder()
    test_api_fallbacks()
    
    print("✅ Test suite completed!")
    print("\n📋 Summary:")
    print("- If you see API configuration errors, add the missing API keys to .env")
    print("- The system will work with partial API configurations using fallbacks")
    print("- Apollo API is working and provides the highest quality results")
    print("- Email verification requires additional API keys for full functionality")
    print("- Company-specific patterns work without any API keys")
    
    print("\n🚀 Next Steps:")
    print("1. Configure additional API keys for better coverage")
    print("2. Test the web interface at /search")
    print("3. Use the 'Enhanced Search' button for best results")
    print("4. Monitor API quotas and upgrade plans as needed")

if __name__ == "__main__":
    main()
