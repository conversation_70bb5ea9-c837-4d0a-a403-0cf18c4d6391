#!/usr/bin/env python3
"""
Test script for Hiring Manager API integration
Tests the RapidAPI Hiring Manager API functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.hiring_manager_api import HiringManagerAPI
import json

def print_section(title):
    """Print a formatted section header"""
    print(f"\n{'='*60}")
    print(f"  {title}")
    print(f"{'='*60}")

def print_result(result):
    """Print a formatted result"""
    print(json.dumps(result, indent=2, default=str))

def test_api_connection():
    """Test the API connection and subscription status"""
    print_section("Testing Hiring Manager API Connection")
    
    api = HiringManagerAPI()
    result = api.test_api_connection()
    
    print_result(result)
    
    if result.get('subscription_required'):
        print("\n⚠️  SUBSCRIPTION REQUIRED")
        print("The API key is valid but you need to subscribe to the Hiring Manager API.")
        print("Next steps:")
        for step in result.get('next_steps', []):
            print(f"  • {step}")
    
    return result.get('success', False)

def test_recruitment_managers():
    """Test getting recruitment managers data"""
    print_section("Testing Recruitment Managers (24h)")
    
    api = HiringManagerAPI()
    result = api.get_recruitment_managers_24h()
    
    print_result(result)
    
    return result.get('success', False)

def test_hiring_manager_search():
    """Test searching for hiring managers"""
    print_section("Testing Hiring Manager Search")
    
    api = HiringManagerAPI()
    
    # Test with different search criteria
    test_cases = [
        {'company_name': 'Google', 'location': 'India'},
        {'company_name': 'Microsoft', 'industry': 'Technology'},
        {'location': 'Bangalore', 'industry': 'IT Services'},
        {'company_name': 'Infosys'}
    ]
    
    for i, criteria in enumerate(test_cases, 1):
        print(f"\n--- Test Case {i}: {criteria} ---")
        result = api.search_hiring_managers(**criteria)
        print_result(result)

def test_company_hiring_data():
    """Test getting company hiring data"""
    print_section("Testing Company Hiring Data")
    
    api = HiringManagerAPI()
    
    # Test with different companies
    companies = ['Google', 'Microsoft', 'Infosys', 'TCS', 'Wipro']
    
    for company in companies:
        print(f"\n--- {company} Hiring Data ---")
        result = api.get_company_hiring_data(company)
        print_result(result)

def show_alternatives():
    """Show alternative APIs"""
    print_section("Alternative APIs for Hiring Manager Data")
    
    api = HiringManagerAPI()
    result = api.get_alternative_apis()
    
    print_result(result)

def main():
    """Main test function"""
    print("🚀 Hiring Manager API Integration Test")
    print("Testing RapidAPI Hiring Manager API functionality...")
    
    # Test API connection first
    connection_success = test_api_connection()
    
    if connection_success:
        print("\n✅ API connection successful! Running full tests...")
        
        # Run all tests
        test_recruitment_managers()
        test_hiring_manager_search()
        test_company_hiring_data()
    else:
        print("\n❌ API connection failed or subscription required.")
        print("Running limited tests and showing alternatives...")
    
    # Always show alternatives
    show_alternatives()
    
    print_section("Test Summary")
    
    if connection_success:
        print("✅ All tests completed successfully!")
        print("The Hiring Manager API is ready to use.")
    else:
        print("⚠️  API subscription required to access hiring manager data.")
        print("Consider using the alternative APIs shown above.")
        print("Your existing Apollo.io integration is already working well!")
    
    print("\n📝 Next Steps:")
    print("1. Subscribe to the Hiring Manager API on RapidAPI if needed")
    print("2. Use the API endpoints in your job search application")
    print("3. Integrate with existing HR finder functionality")
    print("4. Consider combining with Apollo.io for comprehensive results")

if __name__ == "__main__":
    main()
